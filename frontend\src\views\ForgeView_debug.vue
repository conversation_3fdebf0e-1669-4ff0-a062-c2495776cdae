<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-gray-50 to-orange-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo & Title -->
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-800 to-blue-900 rounded-xl flex items-center justify-center">
              <i class="fas fa-hammer text-white text-lg"></i>
            </div>
            <div>
              <h1 class="text-xl font-bold text-gray-900">Forge Dashboard (Debug)</h1>
              <p class="text-sm text-gray-600">System Management</p>
            </div>
          </div>

          <!-- User Menu -->
          <div class="flex items-center space-x-4">
            <!-- Debug Info -->
            <div class="bg-yellow-100 px-3 py-1 rounded text-xs">
              State: {{ showUserMenu ? 'OPEN' : 'CLOSED' }}
            </div>
            
            <!-- Test Buttons -->
            <button @click="testToggle" class="bg-red-500 text-white px-2 py-1 text-xs rounded">
              Test Toggle
            </button>
            
            <button @click="showUserMenu = !showUserMenu" class="bg-green-500 text-white px-2 py-1 text-xs rounded">
              Direct Toggle
            </button>

            <!-- User Dropdown -->
            <div class="relative" ref="userMenuRef">
              <button 
                @click="toggleUserMenu"
                class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-300"
              >
                <div class="w-8 h-8 bg-gradient-to-br from-orange-600 to-orange-800 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-semibold">{{ userInitials }}</span>
                </div>
                <i class="fas fa-chevron-down text-gray-600 text-sm transition-transform duration-200" :class="{ 'rotate-180': showUserMenu }"></i>
              </button>

              <!-- Dropdown Menu -->
              <div 
                v-if="showUserMenu"
                class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
              >
                <div class="px-4 py-2 border-b border-gray-200">
                  <p class="text-sm font-medium text-gray-900">{{ userName }}</p>
                  <p class="text-xs text-gray-600">Forge Admin</p>
                </div>
                <button 
                  @click="handleLogout"
                  class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200 flex items-center space-x-2"
                >
                  <i class="fas fa-sign-out-alt"></i>
                  <span>Logout</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-white rounded-lg p-6 shadow">
        <h2 class="text-lg font-semibold mb-4">Debug Information</h2>
        
        <div class="space-y-2 text-sm">
          <p><strong>Component Mounted:</strong> {{ isMounted ? 'Yes' : 'No' }}</p>
          <p><strong>Show User Menu:</strong> {{ showUserMenu }}</p>
          <p><strong>User Name:</strong> {{ userName }}</p>
          <p><strong>User Initials:</strong> {{ userInitials }}</p>
          <p><strong>Auth Store Loaded:</strong> {{ authStore ? 'Yes' : 'No' }}</p>
          <p><strong>Toggle Function:</strong> {{ typeof toggleUserMenu }}</p>
        </div>
        
        <div class="mt-4 space-x-2">
          <button @click="testReactivity" class="bg-blue-500 text-white px-4 py-2 rounded">
            Test Reactivity
          </button>
          <button @click="logState" class="bg-purple-500 text-white px-4 py-2 rounded">
            Log State
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

console.log('🚀 ForgeView Debug - Script loading...')

// Composables
const router = useRouter()
const authStore = useAuthStore()

// State
const showUserMenu = ref(false)
const userMenuRef = ref(null)
const isMounted = ref(false)

console.log('🚀 ForgeView Debug - State initialized')

// Computed properties
const userName = computed(() => {
  return authStore.user?.first_name && authStore.user?.last_name
    ? `${authStore.user.first_name} ${authStore.user.last_name}`
    : authStore.user?.username || 'Forge Admin'
})

const userInitials = computed(() => {
  if (authStore.user?.first_name && authStore.user?.last_name) {
    return `${authStore.user.first_name[0]}${authStore.user.last_name[0]}`.toUpperCase()
  }
  return authStore.user?.username?.[0]?.toUpperCase() || 'F'
})

console.log('🚀 ForgeView Debug - Computed properties defined')

// Methods
const toggleUserMenu = () => {
  console.log('🔥 toggleUserMenu called!')
  console.log('Current state:', showUserMenu.value)
  showUserMenu.value = !showUserMenu.value
  console.log('New state:', showUserMenu.value)
}

const testToggle = () => {
  console.log('🧪 testToggle called!')
  alert('Test toggle clicked!')
  toggleUserMenu()
}

const testReactivity = () => {
  console.log('🧪 Testing reactivity...')
  showUserMenu.value = !showUserMenu.value
  console.log('Reactivity test - new state:', showUserMenu.value)
}

const logState = () => {
  try {
    console.log('📊 Current state:')
    console.log('showUserMenu:', showUserMenu.value)
    console.log('isMounted:', isMounted.value)
    console.log('authStore:', authStore)
    console.log('userName:', userName.value)
    console.log('userInitials:', userInitials.value)
    alert('State logged to console - check browser console!')
  } catch (error) {
    console.error('Error in logState:', error)
    alert('Error logging state: ' + error.message)
  }
}

const handleLogout = async () => {
  console.log('🚪 Logout clicked')
  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('Logout failed:', error)
    authStore.logout()
    router.push('/')
  }
}

console.log('🚀 ForgeView Debug - Methods defined')

onMounted(() => {
  console.log('🚀 ForgeView Debug - Component mounted!')
  isMounted.value = true
  console.log('Auth store:', authStore)
  console.log('User:', authStore.user)
  console.log('Initial showUserMenu:', showUserMenu.value)
})

console.log('🚀 ForgeView Debug - Script loaded!')
</script>
