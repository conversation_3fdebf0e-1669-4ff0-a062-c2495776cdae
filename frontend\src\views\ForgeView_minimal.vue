<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">Minimal Forge View</h1>
    
    <div class="space-y-4">
      <p>Testing basic functionality...</p>
      
      <div class="bg-white p-4 rounded shadow">
        <p>Menu State: <strong class="text-blue-600">{{ showUserMenu ? 'OPEN' : 'CLOSED' }}</strong></p>
        
        <div class="mt-4 space-x-2">
          <button 
            @click="toggleUserMenu"
            class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Toggle with Function
          </button>
          
          <button 
            @click="showUserMenu = !showUserMenu"
            class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
          >
            Toggle Direct
          </button>
        </div>
        
        <div v-if="showUserMenu" class="mt-4 bg-gray-100 p-4 rounded">
          <p>✅ Menu is visible!</p>
          <button @click="showUserMenu = false" class="bg-red-500 text-white px-2 py-1 rounded text-sm mt-2">
            Close
          </button>
        </div>
      </div>
      
      <div class="bg-yellow-50 p-4 rounded border border-yellow-200">
        <h3 class="font-bold text-yellow-800">Debug Info:</h3>
        <p>Component mounted: {{ isMounted ? 'Yes' : 'No' }}</p>
        <p>User: {{ userName }}</p>
        <p>Auth Store: {{ authStore ? 'Loaded' : 'Not loaded' }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

console.log('🚀 Minimal ForgeView script loading...')

// Basic state
const showUserMenu = ref(false)
const isMounted = ref(false)

// Auth store
const authStore = useAuthStore()

// Computed
const userName = computed(() => {
  return authStore.user?.first_name || authStore.user?.username || 'Unknown User'
})

// Methods
const toggleUserMenu = () => {
  console.log('🔥 Toggle function called!')
  console.log('Current state:', showUserMenu.value)
  showUserMenu.value = !showUserMenu.value
  console.log('New state:', showUserMenu.value)
}

onMounted(() => {
  console.log('🚀 Minimal ForgeView mounted!')
  isMounted.value = true
  console.log('Auth store:', authStore)
  console.log('User:', authStore.user)
})

console.log('🚀 Minimal ForgeView script loaded!')
</script>
