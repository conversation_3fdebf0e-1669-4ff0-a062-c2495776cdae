<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-gray-50 to-orange-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo & Title -->
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-800 to-blue-900 rounded-xl flex items-center justify-center">
              <i class="fas fa-hammer text-white text-lg"></i>
            </div>
            <div>
              <h1 class="text-xl font-bold text-gray-900">Forge Dashboard</h1>
              <p class="text-sm text-gray-600">System Management</p>
            </div>
          </div>

          <!-- User Menu -->
          <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <button class="p-2 text-gray-600 hover:text-blue-800 transition-colors duration-300 relative">
              <i class="fas fa-bell text-lg"></i>
              <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
            </button>

            <!-- User Dropdown -->
            <div class="relative" ref="userMenuRef">
              <button
                @click="toggleUserMenu"
                class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-300"
              >
                <div class="w-8 h-8 bg-gradient-to-br from-orange-600 to-orange-800 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-semibold">{{ userInitials }}</span>
                </div>
                <i class="fas fa-chevron-down text-gray-600 text-sm transition-transform duration-200" :class="{ 'rotate-180': showUserMenu }"></i>
              </button>

              <!-- Dropdown Menu -->
              <transition
                enter-active-class="transition ease-out duration-100"
                enter-from-class="transform opacity-0 scale-95"
                enter-to-class="transform opacity-100 scale-100"
                leave-active-class="transition ease-in duration-75"
                leave-from-class="transform opacity-100 scale-100"
                leave-to-class="transform opacity-0 scale-95"
              >
                <div 
                  v-if="showUserMenu"
                  class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
                >
                  <div class="px-4 py-2 border-b border-gray-200">
                    <p class="text-sm font-medium text-gray-900">{{ userName }}</p>
                    <p class="text-xs text-gray-600">Forge Admin</p>
                  </div>
                  <button 
                    @click="handleLogout"
                    class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200 flex items-center space-x-2"
                  >
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                  </button>
                </div>
              </transition>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Navigation Tabs -->
      <div class="mb-8">
        <nav class="flex space-x-8" aria-label="Tabs">
          <button 
            v-for="tab in tabs" 
            :key="tab.id" 
            @click="activeTab = tab.id" 
            :class="[
              activeTab === tab.id
                ? 'border-blue-800 text-blue-800'
                : 'border-transparent text-gray-600 hover:text-gray-900 hover:border-gray-300',
              'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200'
            ]"
          >
            {{ tab.name }}
          </button>
        </nav>
      </div>

      <!-- Dashboard Tab -->
      <div v-if="activeTab === 'dashboard'">
        <!-- Dashboard Controls -->
        <div class="mb-6">
          <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-200">
            <div class="flex justify-between items-center">
              <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                  <input
                    id="auto-refresh"
                    v-model="autoRefresh"
                    @change="toggleAutoRefresh"
                    type="checkbox"
                    class="rounded border-gray-300 text-blue-800 focus:ring-blue-800"
                  >
                  <label for="auto-refresh" class="text-sm font-medium text-gray-700">Auto-refresh</label>
                </div>
                <select
                  v-if="autoRefresh"
                  v-model="refreshInterval"
                  @change="toggleAutoRefresh"
                  class="text-sm border border-gray-300 rounded px-2 py-1"
                >
                  <option :value="15">15 seconds</option>
                  <option :value="30">30 seconds</option>
                  <option :value="60">1 minute</option>
                  <option :value="300">5 minutes</option>
                </select>
              </div>
              <div class="flex items-center space-x-2 text-sm text-gray-600">
                <i class="fas fa-clock"></i>
                <span>Last updated: {{ lastUpdated }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Total Pods</p>
                <p class="text-3xl font-bold text-gray-900">{{ stats.totalPods }}</p>
              </div>
              <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-building text-blue-800 text-xl"></i>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Active Orders</p>
                <p class="text-3xl font-bold text-gray-900">{{ stats.activeOrders }}</p>
              </div>
              <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-clipboard-list text-green-800 text-xl"></i>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                <p class="text-3xl font-bold text-gray-900">R{{ stats.totalRevenue.toLocaleString() }}</p>
              </div>
              <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-dollar-sign text-orange-800 text-xl"></i>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">System Health</p>
                <p class="text-3xl font-bold text-green-800">{{ stats.systemHealth }}%</p>
              </div>
              <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-check-circle text-green-800 text-xl"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Search and Filters -->
        <div class="mb-8">
          <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Search Businesses</label>
                <div class="relative">
                  <input
                    v-model="searchQuery"
                    type="text"
                    placeholder="Search by name or owner..."
                    class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-800 focus:border-transparent"
                    @input="debouncedSearch"
                  >
                  <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Business Status</label>
                <select
                  v-model="filters.status"
                  @change="applyFilters"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-800 focus:border-transparent"
                >
                  <option value="">All Statuses</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="suspended">Suspended</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Business Type</label>
                <select
                  v-model="filters.business_type"
                  @change="applyFilters"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-800 focus:border-transparent"
                >
                  <option value="">All Types</option>
                  <option value="product_goods">Product & Goods</option>
                  <option value="service">Service Business</option>
                  <option value="food_restaurant">Food & Restaurant</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Subscription</label>
                <select
                  v-model="filters.subscription_status"
                  @change="applyFilters"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-800 focus:border-transparent"
                >
                  <option value="">All Subscriptions</option>
                  <option value="active">Active Subscription</option>
                  <option value="expired">Expired</option>
                  <option value="pending">Pending Payment</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="mb-8">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              @click="openBusinessModal(null, 'create')"
              class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300 text-left group"
            >
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center group-hover:bg-blue-800 transition-colors duration-300">
                  <i class="fas fa-plus text-blue-800 group-hover:text-white text-xl transition-colors duration-300"></i>
                </div>
                <div>
                  <h3 class="font-semibold text-gray-900">Add New Pod</h3>
                  <p class="text-sm text-gray-600">Register a new business</p>
                </div>
              </div>
            </button>

            <button
              @click="activeTab = 'reports'"
              class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300 text-left group"
            >
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center group-hover:bg-orange-800 transition-colors duration-300">
                  <i class="fas fa-chart-bar text-orange-800 group-hover:text-white text-xl transition-colors duration-300"></i>
                </div>
                <div>
                  <h3 class="font-semibold text-gray-900">View Analytics</h3>
                  <p class="text-sm text-gray-600">System-wide reports</p>
                </div>
              </div>
            </button>

            <button
              @click="refreshAllData"
              class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300 text-left group"
            >
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center group-hover:bg-gray-800 transition-colors duration-300">
                  <i class="fas fa-sync-alt text-gray-800 group-hover:text-white text-xl transition-colors duration-300"></i>
                </div>
                <div>
                  <h3 class="font-semibold text-gray-900">Refresh Data</h3>
                  <p class="text-sm text-gray-600">Update all information</p>
                </div>
              </div>
            </button>
          </div>
        </div>

        <!-- Pods Management -->
        <div class="bg-white rounded-2xl shadow-sm border border-gray-200">
          <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <div>
                <h2 class="text-lg font-semibold text-gray-900">Registered Pods</h2>
                <p class="text-sm text-gray-600 mt-1">
                  {{ filteredBusinesses.length }} of {{ businesses.length }} businesses
                  {{ searchQuery ? `matching "${searchQuery}"` : '' }}
                </p>
              </div>
              <div class="flex space-x-3">
                <button
                  @click="exportBusinessData"
                  class="bg-gray-100 text-gray-700 px-4 py-2 rounded-xl hover:bg-gray-200 transition-all duration-300"
                >
                  <i class="fas fa-download mr-2"></i>
                  Export
                </button>
                <button
                  @click="openBusinessModal(null, 'create')"
                  class="bg-gradient-to-r from-blue-800 to-blue-900 text-white px-4 py-2 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                >
                  <i class="fas fa-plus mr-2"></i>
                  Add Pod
                </button>
              </div>
            </div>
          </div>

          <div class="p-6">
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead>
                  <tr class="border-b border-gray-200">
                    <th class="text-left py-3 px-4 font-semibold text-gray-900">Business Name</th>
                    <th class="text-left py-3 px-4 font-semibold text-gray-900">Owner</th>
                    <th class="text-left py-3 px-4 font-semibold text-gray-900">Status</th>
                    <th class="text-left py-3 px-4 font-semibold text-gray-900">Orders</th>
                    <th class="text-left py-3 px-4 font-semibold text-gray-900">Revenue</th>
                    <th class="text-left py-3 px-4 font-semibold text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="business in filteredBusinesses" :key="business.id" class="border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200">
                    <td class="py-4 px-4">
                      <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-orange-600 to-orange-800 rounded-xl flex items-center justify-center">
                          <span class="text-white font-semibold text-sm">{{ business.name?.charAt(0) || 'B' }}</span>
                        </div>
                        <div>
                          <p class="font-medium text-gray-900">{{ business.name }}</p>
                          <p class="text-sm text-gray-600">{{ business.business_type_display }}</p>
                        </div>
                      </div>
                    </td>
                    <td class="py-4 px-4 text-gray-900">{{ business.owner_name || 'N/A' }}</td>
                    <td class="py-4 px-4">
                      <span :class="getStatusClass(business.status)" class="px-3 py-1 rounded-full text-xs font-medium">
                        {{ business.status_display }}
                      </span>
                    </td>
                    <td class="py-4 px-4 text-gray-900">{{ business.total_orders || 0 }}</td>
                    <td class="py-4 px-4 text-gray-900">R{{ (business.total_revenue || 0).toLocaleString() }}</td>
                    <td class="py-4 px-4">
                      <div class="flex space-x-2">
                        <button @click="openBusinessModal(business, 'view')" class="p-2 text-blue-800 hover:bg-blue-100 rounded-lg transition-colors duration-200" title="View Details">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button @click="openBusinessModal(business, 'edit')" class="p-2 text-orange-800 hover:bg-orange-100 rounded-lg transition-colors duration-200" title="Edit Business">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button @click="openBusinessModal(business, 'action')" class="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200" title="Manage Status">
                          <i class="fas fa-ellipsis-v"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center items-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-800"></div>
          <span class="ml-2 text-gray-600">Loading businesses...</span>
        </div>

        <!-- Empty State -->
        <div v-else-if="filteredBusinesses.length === 0 && !isLoading" class="text-center py-8">
          <i class="fas fa-building text-gray-400 text-6xl mb-4"></i>
          <h3 class="mt-2 text-sm font-medium text-gray-900">
            {{ businesses.length === 0 ? 'No businesses found' : 'No businesses match your filters' }}
          </h3>
          <p class="mt-1 text-sm text-gray-600">
            {{ businesses.length === 0 ? 'Get started by registering your first business.' : 'Try adjusting your search or filters.' }}
          </p>
          <button
            v-if="businesses.length === 0"
            @click="openBusinessModal(null, 'create')"
            class="mt-4 bg-blue-800 text-white px-4 py-2 rounded-lg hover:bg-blue-900 transition-colors"
          >
            <i class="fas fa-plus mr-2"></i>
            Create First Business
          </button>
        </div>
      </div>

      <!-- Subscription Reports Tab -->
      <div v-if="activeTab === 'reports'">
        <SubscriptionReports />
      </div>

      <!-- Subscription Approvals Tab -->
      <div v-if="activeTab === 'approvals'">
        <SubscriptionApproval />
      </div>
    </main>

    <!-- Business Modal -->
    <BusinessModal
      :is-open="businessModal.isOpen"
      :business="businessModal.business"
      :mode="businessModal.mode"
      @close="closeBusinessModal"
      @save="handleBusinessSave"
      @create="handleBusinessCreate"
      @status-change="handleBusinessStatusChange"
    />

    <!-- Notification Toast -->
    <NotificationToast ref="notificationToast" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import BusinessModal from '@/components/forge/BusinessModal.vue'
import SubscriptionReports from '@/components/forge/SubscriptionReports.vue'
import SubscriptionApproval from '@/components/forge/SubscriptionApproval.vue'
import NotificationToast from '@/components/NotificationToast.vue'
import { businessService, reportsService } from '@/services/forgeApi'

// Composables
const router = useRouter()
const authStore = useAuthStore()

// User menu state
const showUserMenu = ref(false)
const userMenuRef = ref(null)

// User info computed properties
const userName = computed(() => {
  return authStore.user?.first_name && authStore.user?.last_name
    ? `${authStore.user.first_name} ${authStore.user.last_name}`
    : authStore.user?.username || 'Forge Admin'
})

const userInitials = computed(() => {
  if (authStore.user?.first_name && authStore.user?.last_name) {
    return `${authStore.user.first_name[0]}${authStore.user.last_name[0]}`.toUpperCase()
  }
  return authStore.user?.username?.[0]?.toUpperCase() || 'F'
})

// Reactive data
const stats = ref({
  totalPods: 0,
  activeOrders: 0,
  totalRevenue: 0,
  systemHealth: 98
})

const businesses = ref([])
const isLoading = ref(false)
const error = ref(null)

// Search and filter state
const searchQuery = ref('')
const filters = ref({
  status: '',
  business_type: '',
  subscription_status: ''
})

// Debounced search
let searchTimeout = null

// Auto-refresh functionality
const autoRefresh = ref(false)
const refreshInterval = ref(30) // seconds
let refreshTimer = null

// Business modal state
const businessModal = ref({
  isOpen: false,
  business: null,
  mode: 'view' // 'view', 'edit', 'action'
})

// Navigation state
const activeTab = ref('dashboard')
const tabs = ref([
  { id: 'dashboard', name: 'Dashboard' },
  { id: 'reports', name: 'Subscription Reports' },
  { id: 'approvals', name: 'Payment Approvals' }
])

// Notification toast ref
const notificationToast = ref(null)

// Last updated timestamp
const lastUpdated = ref('')

// Update last updated timestamp
const updateLastUpdated = () => {
  lastUpdated.value = new Date().toLocaleTimeString()
}

// Computed properties
const filteredBusinesses = computed(() => {
  let filtered = businesses.value

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(business =>
      business.name?.toLowerCase().includes(query) ||
      business.owner_name?.toLowerCase().includes(query) ||
      business.business_email?.toLowerCase().includes(query)
    )
  }

  // Apply status filter
  if (filters.value.status) {
    filtered = filtered.filter(business => business.status === filters.value.status)
  }

  // Apply business type filter
  if (filters.value.business_type) {
    filtered = filtered.filter(business => business.business_type === filters.value.business_type)
  }

  // Apply subscription status filter (this would need subscription data)
  if (filters.value.subscription_status) {
    // This would require subscription data to be loaded with businesses
    // For now, we'll skip this filter
  }

  return filtered
})

// Methods
const getStatusClass = (status) => {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800'
    case 'suspended':
      return 'bg-yellow-100 text-yellow-800'
    case 'inactive':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// Search and filter methods
const debouncedSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    // The computed property will automatically update
  }, 300)
}

const applyFilters = () => {
  // The computed property will automatically update
}

const exportBusinessData = () => {
  const csvContent = [
    ['Business Name', 'Owner', 'Type', 'Status', 'Phone', 'Email', 'Orders', 'Revenue'],
    ...filteredBusinesses.value.map(business => [
      business.name || '',
      business.owner_name || '',
      business.business_type_display || '',
      business.status_display || '',
      business.business_phone || '',
      business.business_email || '',
      business.total_orders || 0,
      business.total_revenue || 0
    ])
  ].map(row => row.join(',')).join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `businesses_${new Date().toISOString().split('T')[0]}.csv`
  a.click()
  window.URL.revokeObjectURL(url)
}

const refreshAllData = async () => {
  await Promise.all([
    loadBusinesses(),
    loadSystemStats()
  ])

  updateLastUpdated()

  notificationToast.value?.showSuccess(
    'Data Refreshed',
    'All dashboard data has been updated successfully.'
  )
}

// Auto-refresh methods
const toggleAutoRefresh = () => {
  if (autoRefresh.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  stopAutoRefresh() // Clear any existing timer
  refreshTimer = setInterval(() => {
    refreshAllData()
  }, refreshInterval.value * 1000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// User menu methods
const toggleUserMenu = (event) => {
  event?.stopPropagation()
  showUserMenu.value = !showUserMenu.value
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('Logout failed:', error)
    // Force logout even if API call fails
    authStore.logout()
    router.push('/')
  }
}

// Close user menu when clicking outside
const handleClickOutside = (event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target)) {
    showUserMenu.value = false
  }
}

// Data loading functions
const loadBusinesses = async () => {
  isLoading.value = true
  error.value = null
  try {
    const data = await businessService.getAllBusinesses()
    businesses.value = data

    // Update stats
    stats.value.totalPods = data.length
    stats.value.activeOrders = data.reduce((sum, business) => sum + (business.total_orders || 0), 0)
    stats.value.totalRevenue = data.reduce((sum, business) => sum + (business.total_revenue || 0), 0)

    updateLastUpdated()
  } catch (err) {
    error.value = err.message
    console.error('Failed to load businesses:', err)
  } finally {
    isLoading.value = false
  }
}

const loadSystemStats = async () => {
  try {
    const data = await reportsService.getSystemDashboard()
    stats.value = {
      totalPods: data.business_stats?.total_businesses || 0,
      activeOrders: data.order_stats?.total_orders || 0,
      totalRevenue: data.revenue_stats?.total_revenue || 0,
      systemHealth: 98 // This could come from a health check endpoint
    }

    updateLastUpdated()
  } catch (err) {
    console.error('Failed to load system stats:', err)
  }
}

// Business modal methods
const openBusinessModal = (business, mode) => {
  businessModal.value = {
    isOpen: true,
    business,
    mode
  }
}

const closeBusinessModal = () => {
  businessModal.value = {
    isOpen: false,
    business: null,
    mode: 'view'
  }
}

const handleBusinessSave = async (businessData) => {
  try {
    const businessId = businessModal.value.business.id
    await businessService.updateBusiness(businessId, businessData)

    // Refresh businesses list
    await loadBusinesses()
    closeBusinessModal()

    // Show success notification
    notificationToast.value?.showSuccess(
      'Business Updated',
      'Business information has been updated successfully.'
    )
  } catch (err) {
    console.error('Failed to update business:', err)
    // Show error notification
    notificationToast.value?.showError(
      'Update Failed',
      err.message || 'Failed to update business information.'
    )
  }
}

const handleBusinessCreate = async (businessData) => {
  try {
    await businessService.createBusiness(businessData)

    // Refresh businesses list
    await loadBusinesses()
    closeBusinessModal()

    // Show success notification
    notificationToast.value?.showSuccess(
      'Business Created',
      `${businessData.name} has been created successfully.`
    )
  } catch (err) {
    console.error('Failed to create business:', err)
    // Show error notification
    notificationToast.value?.showError(
      'Creation Failed',
      err.message || 'Failed to create business.'
    )
  }
}

const handleBusinessStatusChange = async (newStatus) => {
  try {
    const businessId = businessModal.value.business.id
    await businessService.updateBusinessStatus(businessId, newStatus)

    // Refresh businesses list
    await loadBusinesses()
    closeBusinessModal()

    // Show success notification
    notificationToast.value?.showSuccess(
      'Status Updated',
      `Business status has been updated to ${newStatus}.`
    )
  } catch (err) {
    console.error('Failed to update business status:', err)
    // Show error notification
    notificationToast.value?.showError(
      'Status Update Failed',
      err.message || 'Failed to update business status.'
    )
  }
}

onMounted(async () => {
  // Load data on component mount
  console.log('Forge Dashboard loaded')

  // Add click outside listener for user menu (with delay to avoid immediate closure)
  setTimeout(() => {
    document.addEventListener('click', handleClickOutside)
  }, 100)

  // Load initial data
  await Promise.all([
    loadBusinesses(),
    loadSystemStats()
  ])
})

onUnmounted(() => {
  // Remove click outside listener
  document.removeEventListener('click', handleClickOutside)

  // Stop auto-refresh timer
  stopAutoRefresh()
})
</script>
