<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <h2 class="text-2xl font-bold text-[#111111]">Subscription Reports</h2>
      <div class="flex space-x-3">
        <select
          v-model="selectedMonth"
          @change="loadReports"
          class="px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
        >
          <option value="">All Time</option>
          <option v-for="month in availableMonths" :key="month.value" :value="month.value">
            {{ month.label }}
          </option>
        </select>
        <button
          @click="refreshData"
          :disabled="isLoading"
          class="px-4 py-2 bg-[#1E4E79] text-white rounded-lg hover:bg-[#132F4C] transition-colors disabled:opacity-50"
        >
          <i class="fas fa-sync-alt mr-2"></i>
          {{ isLoading ? 'Refreshing...' : 'Refresh' }}
        </button>
      </div>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Subscriptions -->
      <div class="bg-white rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-[#4B4B4B]">Total Subscriptions</p>
            <p class="text-3xl font-bold text-[#111111]">{{ reports.subscription_stats?.total_subscriptions || 0 }}</p>
          </div>
          <div class="w-12 h-12 bg-[#1E4E79]/10 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-[#1E4E79]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
          </div>
        </div>
      </div>

      <!-- Active Subscriptions -->
      <div class="bg-white rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-[#4B4B4B]">Active Subscriptions</p>
            <p class="text-3xl font-bold text-[#2E7D32]">{{ reports.subscription_stats?.active_subscriptions || 0 }}</p>
          </div>
          <div class="w-12 h-12 bg-[#2E7D32]/10 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-[#2E7D32]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
            </svg>
          </div>
        </div>
      </div>

      <!-- Pending Payments -->
      <div class="bg-white rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-[#4B4B4B]">Pending Payments</p>
            <p class="text-3xl font-bold text-[#FF8F00]">{{ reports.payment_stats?.pending_payments || 0 }}</p>
          </div>
          <div class="w-12 h-12 bg-[#FF8F00]/10 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-[#FF8F00]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
        </div>
      </div>

      <!-- Monthly Revenue -->
      <div class="bg-white rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-[#4B4B4B]">Monthly Revenue</p>
            <p class="text-3xl font-bold text-[#C1843E]">R{{ (reports.revenue_stats?.monthly_revenue || 0).toLocaleString() }}</p>
          </div>
          <div class="w-12 h-12 bg-[#C1843E]/10 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-[#C1843E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Plan Distribution -->
    <div class="bg-white rounded-2xl shadow-sm border border-[#CCCCCC]/30">
      <div class="p-6 border-b border-[#CCCCCC]/30">
        <h3 class="text-lg font-semibold text-[#111111]">Subscription Plan Distribution</h3>
      </div>
      <div class="p-6">
        <div v-if="reports.plan_distribution?.length > 0" class="space-y-4">
          <div v-for="plan in reports.plan_distribution" :key="plan.name" class="flex items-center justify-between p-4 bg-[#F5F5F5] rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="w-3 h-3 rounded-full" :class="getPlanColor(plan.plan_type)"></div>
              <div>
                <p class="font-medium text-[#111111]">{{ plan.name }}</p>
                <p class="text-sm text-[#4B4B4B]">{{ plan.plan_type }}</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-lg font-bold text-[#111111]">{{ plan.subscription_count }}</p>
              <p class="text-sm text-[#4B4B4B]">subscriptions</p>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8">
          <p class="text-[#4B4B4B]">No subscription plans data available</p>
        </div>
      </div>
    </div>

    <!-- Monthly Subscription Breakdown -->
    <div v-if="selectedMonth" class="bg-white rounded-2xl shadow-sm border border-[#CCCCCC]/30">
      <div class="p-6 border-b border-[#CCCCCC]/30">
        <h3 class="text-lg font-semibold text-[#111111]">
          {{ getMonthLabel(selectedMonth) }} Subscription Activity
        </h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div class="bg-[#F5F5F5] rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">New Subscriptions</p>
                <p class="text-2xl font-bold text-[#2E7D32]">{{ monthlyStats.new_subscriptions || 0 }}</p>
              </div>
              <i class="fas fa-plus-circle text-[#2E7D32] text-xl"></i>
            </div>
          </div>
          <div class="bg-[#F5F5F5] rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">Renewals</p>
                <p class="text-2xl font-bold text-[#1E4E79]">{{ monthlyStats.renewals || 0 }}</p>
              </div>
              <i class="fas fa-redo text-[#1E4E79] text-xl"></i>
            </div>
          </div>
          <div class="bg-[#F5F5F5] rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">Revenue</p>
                <p class="text-2xl font-bold text-[#C1843E]">R{{ (monthlyStats.revenue || 0).toLocaleString() }}</p>
              </div>
              <i class="fas fa-dollar-sign text-[#C1843E] text-xl"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Payments -->
    <div class="bg-white rounded-2xl shadow-sm border border-[#CCCCCC]/30">
      <div class="p-6 border-b border-[#CCCCCC]/30">
        <h3 class="text-lg font-semibold text-[#111111]">
          {{ selectedMonth ? `${getMonthLabel(selectedMonth)} Payments` : 'Recent Subscription Payments' }}
        </h3>
      </div>
      <div class="p-6">
        <div v-if="reports.recent_payments?.length > 0" class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b border-[#CCCCCC]/30">
                <th class="text-left py-3 px-4 font-semibold text-[#111111]">Business</th>
                <th class="text-left py-3 px-4 font-semibold text-[#111111]">Plan</th>
                <th class="text-left py-3 px-4 font-semibold text-[#111111]">Amount</th>
                <th class="text-left py-3 px-4 font-semibold text-[#111111]">Status</th>
                <th class="text-left py-3 px-4 font-semibold text-[#111111]">Date</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="payment in reports.recent_payments" :key="payment.id" class="border-b border-[#CCCCCC]/20 hover:bg-[#F5F5F5] transition-colors">
                <td class="py-4 px-4">
                  <div>
                    <p class="font-medium text-[#111111]">{{ payment.business_name }}</p>
                    <p class="text-sm text-[#4B4B4B]">{{ payment.business_owner }}</p>
                  </div>
                </td>
                <td class="py-4 px-4 text-[#111111]">{{ payment.plan_name }}</td>
                <td class="py-4 px-4 text-[#111111]">R{{ payment.amount }}</td>
                <td class="py-4 px-4">
                  <span :class="getPaymentStatusClass(payment.status)" class="px-3 py-1 rounded-full text-xs font-medium">
                    {{ payment.status_display }}
                  </span>
                </td>
                <td class="py-4 px-4 text-[#4B4B4B]">{{ formatDate(payment.created_at) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else class="text-center py-8">
          <i class="fas fa-receipt text-[#CCCCCC] text-4xl mb-4"></i>
          <p class="text-[#4B4B4B]">
            {{ selectedMonth ? `No payments found for ${getMonthLabel(selectedMonth)}` : 'No recent payments found' }}
          </p>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#1E4E79]"></div>
      <span class="ml-2 text-[#4B4B4B]">Loading reports...</span>
    </div>

    <!-- Error State -->
    <div v-if="error" class="bg-[#C62828]/5 border border-[#C62828]/20 rounded-lg p-4">
      <div class="flex items-center space-x-2">
        <svg class="w-5 h-5 text-[#C62828]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        <p class="text-[#C62828] font-medium">Error loading reports</p>
      </div>
      <p class="text-[#C62828] text-sm mt-1">{{ error }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { reportsService } from '@/services/forgeApi'

// State
const reports = ref({
  subscription_stats: {},
  payment_stats: {},
  revenue_stats: {},
  recent_payments: [],
  plan_distribution: []
})
const monthlyStats = ref({
  new_subscriptions: 0,
  renewals: 0,
  revenue: 0
})
const isLoading = ref(false)
const error = ref(null)
const selectedMonth = ref('')

// Generate available months (current and previous 12 months)
const availableMonths = ref([])

const generateAvailableMonths = () => {
  const months = []
  const now = new Date()

  for (let i = 0; i < 12; i++) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
    const value = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
    const label = date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' })
    months.push({ value, label })
  }

  availableMonths.value = months
}

// Methods
const loadReports = async () => {
  isLoading.value = true
  error.value = null
  try {
    const params = selectedMonth.value ? { month: selectedMonth.value } : {}
    const data = await reportsService.getSubscriptionReports(params)
    reports.value = data

    // Load monthly stats if a specific month is selected
    if (selectedMonth.value) {
      await loadMonthlyStats()
    }
  } catch (err) {
    error.value = err.message
    console.error('Failed to load subscription reports:', err)
  } finally {
    isLoading.value = false
  }
}

const loadMonthlyStats = async () => {
  try {
    // This would be a separate API call for monthly statistics
    // For now, we'll calculate from the existing data
    const payments = reports.value.recent_payments || []
    const monthPayments = payments.filter(payment => {
      const paymentDate = new Date(payment.created_at)
      const [year, month] = selectedMonth.value.split('-')
      return paymentDate.getFullYear() === parseInt(year) &&
             paymentDate.getMonth() + 1 === parseInt(month)
    })

    monthlyStats.value = {
      new_subscriptions: monthPayments.filter(p => p.subscription_type === 'new').length,
      renewals: monthPayments.filter(p => p.subscription_type === 'renewal').length,
      revenue: monthPayments.reduce((sum, p) => sum + parseFloat(p.amount || 0), 0)
    }
  } catch (err) {
    console.error('Failed to load monthly stats:', err)
  }
}

const refreshData = () => {
  loadReports()
}

const getPlanColor = (planType) => {
  switch (planType) {
    case 'basic':
      return 'bg-[#2E7D32]'
    case 'premium':
      return 'bg-[#1E4E79]'
    case 'enterprise':
      return 'bg-[#C1843E]'
    default:
      return 'bg-[#4B4B4B]'
  }
}

const getPaymentStatusClass = (status) => {
  switch (status) {
    case 'approved':
      return 'bg-[#2E7D32]/10 text-[#2E7D32]'
    case 'submitted':
      return 'bg-[#FF8F00]/10 text-[#FF8F00]'
    case 'pending':
      return 'bg-[#4B4B4B]/10 text-[#4B4B4B]'
    case 'rejected':
      return 'bg-[#C62828]/10 text-[#C62828]'
    default:
      return 'bg-[#4B4B4B]/10 text-[#4B4B4B]'
  }
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getMonthLabel = (monthValue) => {
  if (!monthValue) return ''
  const [year, month] = monthValue.split('-')
  const date = new Date(parseInt(year), parseInt(month) - 1, 1)
  return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' })
}

// Lifecycle
onMounted(() => {
  generateAvailableMonths()
  loadReports()
})
</script>
