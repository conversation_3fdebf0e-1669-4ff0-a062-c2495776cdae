<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="closeModal"></div>

    <!-- Modal -->
    <div class="flex min-h-full items-center justify-center p-4">
      <div class="relative bg-white rounded-2xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-[#CCCCCC]/30">
          <h2 class="text-xl font-bold text-[#111111]">
            {{ getModalTitle() }}
          </h2>
          <button @click="closeModal" class="p-2 hover:bg-[#F5F5F5] rounded-lg transition-colors">
            <i class="fas fa-times text-[#4B4B4B]"></i>
          </button>
        </div>

        <!-- Content -->
        <div class="p-6">
          <!-- Create Mode -->
          <div v-if="mode === 'create'" class="space-y-6">
            <form @submit.prevent="handleCreate">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Business Name *</label>
                  <input
                    v-model="createForm.name"
                    type="text"
                    class="w-full px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
                    required
                    placeholder="Enter business name"
                  >
                </div>
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Business Type *</label>
                  <select
                    v-model="createForm.business_type"
                    class="w-full px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
                    required
                  >
                    <option value="">Select business type</option>
                    <option value="product_goods">Product & Goods</option>
                    <option value="service">Service Business</option>
                    <option value="food_restaurant">Food & Restaurant</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Owner Username *</label>
                  <input
                    v-model="createForm.owner_username"
                    type="text"
                    class="w-full px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
                    required
                    placeholder="Enter owner username"
                  >
                  <p class="text-xs text-[#4B4B4B] mt-1">Username of the Pod user who will own this business</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Phone</label>
                  <input
                    v-model="createForm.business_phone"
                    type="tel"
                    class="w-full px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
                    placeholder="Enter business phone"
                  >
                </div>
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Email</label>
                  <input
                    v-model="createForm.business_email"
                    type="email"
                    class="w-full px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
                    placeholder="Enter business email"
                  >
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Address</label>
                <textarea
                  v-model="createForm.address"
                  rows="3"
                  class="w-full px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
                  placeholder="Enter business address"
                ></textarea>
              </div>

              <div>
                <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Description</label>
                <textarea
                  v-model="createForm.description"
                  rows="4"
                  class="w-full px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
                  placeholder="Describe the business"
                ></textarea>
              </div>
            </form>
          </div>

          <!-- View Mode -->
          <div v-else-if="mode === 'view'" class="space-y-6">
            <!-- Business Info Tab -->
            <div class="border-b border-[#CCCCCC]/30">
              <nav class="flex space-x-8" aria-label="Tabs">
                <button
                  v-for="tab in viewTabs"
                  :key="tab.id"
                  @click="activeViewTab = tab.id"
                  :class="[
                    activeViewTab === tab.id
                      ? 'border-[#1E4E79] text-[#1E4E79]'
                      : 'border-transparent text-[#4B4B4B] hover:text-[#111111] hover:border-[#CCCCCC]',
                    'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200'
                  ]"
                >
                  <i :class="tab.icon" class="mr-2"></i>
                  {{ tab.name }}
                </button>
              </nav>
            </div>

            <!-- Basic Info Tab -->
            <div v-if="activeViewTab === 'info'" class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Business Name</label>
                  <p class="text-[#111111] font-medium">{{ business?.name || 'N/A' }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Owner</label>
                  <p class="text-[#111111]">{{ business?.owner_name || 'N/A' }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Business Type</label>
                  <p class="text-[#111111]">{{ business?.business_type_display || 'N/A' }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Status</label>
                  <span :class="getStatusClass(business?.status)" class="px-3 py-1 rounded-full text-sm font-medium">
                    {{ business?.status_display || 'N/A' }}
                  </span>
                </div>
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Phone</label>
                  <p class="text-[#111111]">{{ business?.business_phone || 'N/A' }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Email</label>
                  <p class="text-[#111111]">{{ business?.business_email || 'N/A' }}</p>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Address</label>
                <p class="text-[#111111]">{{ business?.address || 'N/A' }}</p>
              </div>

              <div>
                <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Description</label>
                <p class="text-[#111111]">{{ business?.description || 'N/A' }}</p>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Created</label>
                  <p class="text-[#111111]">{{ formatDate(business?.created_at) }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Last Updated</label>
                  <p class="text-[#111111]">{{ formatDate(business?.updated_at) }}</p>
                </div>
              </div>
            </div>

            <!-- Subscription Tab -->
            <div v-else-if="activeViewTab === 'subscription'" class="space-y-6">
              <div v-if="businessSubscriptions.length > 0">
                <div v-for="subscription in businessSubscriptions" :key="subscription.id" class="bg-[#F5F5F5] rounded-lg p-4 mb-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Plan</label>
                      <p class="text-[#111111] font-medium">{{ subscription.plan_name }}</p>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Status</label>
                      <span :class="getSubscriptionStatusClass(subscription.status)" class="px-3 py-1 rounded-full text-xs font-medium">
                        {{ subscription.status_display }}
                      </span>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Amount</label>
                      <p class="text-[#111111]">R{{ subscription.amount }}</p>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Billing Cycle</label>
                      <p class="text-[#111111]">{{ subscription.billing_cycle_display }}</p>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-[#4B4B4B] mb-1">Start Date</label>
                      <p class="text-[#111111]">{{ formatDate(subscription.start_date) }}</p>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-[#4B4B4B] mb-1">End Date</label>
                      <p class="text-[#111111]">{{ formatDate(subscription.end_date) }}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="text-center py-8">
                <i class="fas fa-credit-card text-[#CCCCCC] text-4xl mb-4"></i>
                <p class="text-[#4B4B4B]">No subscriptions found for this business</p>
              </div>
            </div>

            <!-- Statistics Tab -->
            <div v-else-if="activeViewTab === 'stats'" class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-[#F5F5F5] rounded-lg p-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="text-sm font-medium text-[#4B4B4B]">Total Orders</p>
                      <p class="text-2xl font-bold text-[#111111]">{{ business?.total_orders || 0 }}</p>
                    </div>
                    <i class="fas fa-shopping-cart text-[#1E4E79] text-xl"></i>
                  </div>
                </div>
                <div class="bg-[#F5F5F5] rounded-lg p-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="text-sm font-medium text-[#4B4B4B]">Total Revenue</p>
                      <p class="text-2xl font-bold text-[#111111]">R{{ (business?.total_revenue || 0).toLocaleString() }}</p>
                    </div>
                    <i class="fas fa-dollar-sign text-[#2E7D32] text-xl"></i>
                  </div>
                </div>
                <div class="bg-[#F5F5F5] rounded-lg p-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="text-sm font-medium text-[#4B4B4B]">Products</p>
                      <p class="text-2xl font-bold text-[#111111]">{{ business?.total_products || 0 }}</p>
                    </div>
                    <i class="fas fa-box text-[#C1843E] text-xl"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Edit Mode -->
          <div v-else-if="mode === 'edit'" class="space-y-6">
            <form @submit.prevent="handleSave">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Business Name</label>
                  <input 
                    v-model="editForm.name"
                    type="text" 
                    class="w-full px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
                    required
                  >
                </div>
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Business Type</label>
                  <select 
                    v-model="editForm.business_type"
                    class="w-full px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
                  >
                    <option value="product_goods">Product & Goods</option>
                    <option value="service">Service Business</option>
                    <option value="food_restaurant">Food & Restaurant</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Phone</label>
                  <input 
                    v-model="editForm.business_phone"
                    type="tel" 
                    class="w-full px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
                  >
                </div>
                <div>
                  <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Email</label>
                  <input 
                    v-model="editForm.business_email"
                    type="email" 
                    class="w-full px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
                  >
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Address</label>
                <textarea 
                  v-model="editForm.address"
                  rows="3"
                  class="w-full px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
                ></textarea>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-[#4B4B4B] mb-2">Description</label>
                <textarea 
                  v-model="editForm.description"
                  rows="4"
                  class="w-full px-3 py-2 border border-[#CCCCCC] rounded-lg focus:ring-2 focus:ring-[#1E4E79] focus:border-transparent"
                ></textarea>
              </div>
            </form>
          </div>

          <!-- Action Mode -->
          <div v-else-if="mode === 'action'" class="space-y-4">
            <p class="text-[#4B4B4B]">
              Choose an action for <strong>{{ business?.name }}</strong>:
            </p>
            
            <div class="space-y-3">
              <button 
                v-if="business?.status !== 'active'"
                @click="handleStatusChange('active')"
                class="w-full p-3 text-left rounded-lg border border-[#2E7D32] bg-[#2E7D32]/5 hover:bg-[#2E7D32]/10 transition-colors"
              >
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-[#2E7D32] rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium text-[#2E7D32]">Activate Business</p>
                    <p class="text-sm text-[#4B4B4B]">Allow business to operate normally</p>
                  </div>
                </div>
              </button>
              
              <button 
                v-if="business?.status === 'active'"
                @click="handleStatusChange('suspended')"
                class="w-full p-3 text-left rounded-lg border border-[#FF8F00] bg-[#FF8F00]/5 hover:bg-[#FF8F00]/10 transition-colors"
              >
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-[#FF8F00] rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium text-[#FF8F00]">Suspend Business</p>
                    <p class="text-sm text-[#4B4B4B]">Temporarily disable business operations</p>
                  </div>
                </div>
              </button>
              
              <button 
                @click="handleStatusChange('inactive')"
                class="w-full p-3 text-left rounded-lg border border-[#C62828] bg-[#C62828]/5 hover:bg-[#C62828]/10 transition-colors"
              >
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-[#C62828] rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium text-[#C62828]">Deactivate Business</p>
                    <p class="text-sm text-[#4B4B4B]">Permanently disable business operations</p>
                  </div>
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="flex justify-end space-x-3 p-6 border-t border-[#CCCCCC]/30">
          <button
            @click="closeModal"
            class="px-4 py-2 text-[#4B4B4B] hover:bg-[#F5F5F5] rounded-lg transition-colors"
          >
            {{ mode === 'edit' || mode === 'create' ? 'Cancel' : 'Close' }}
          </button>

          <button
            v-if="mode === 'edit'"
            @click="handleSave"
            :disabled="isLoading"
            class="px-4 py-2 bg-[#1E4E79] text-white rounded-lg hover:bg-[#132F4C] transition-colors disabled:opacity-50"
          >
            <i class="fas fa-save mr-2"></i>
            {{ isLoading ? 'Saving...' : 'Save Changes' }}
          </button>

          <button
            v-if="mode === 'create'"
            @click="handleCreate"
            :disabled="isLoading || !isCreateFormValid"
            class="px-4 py-2 bg-[#1E4E79] text-white rounded-lg hover:bg-[#132F4C] transition-colors disabled:opacity-50"
          >
            <i class="fas fa-plus mr-2"></i>
            {{ isLoading ? 'Creating...' : 'Create Business' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue'
import { subscriptionService } from '@/services/forgeApi'

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  business: {
    type: Object,
    default: null
  },
  mode: {
    type: String,
    default: 'view', // 'view', 'edit', 'action', 'create'
    validator: (value) => ['view', 'edit', 'action', 'create'].includes(value)
  }
})

// Emits
const emit = defineEmits(['close', 'save', 'status-change', 'create'])

// State
const isLoading = ref(false)
const activeViewTab = ref('info')
const businessSubscriptions = ref([])

const editForm = ref({
  name: '',
  business_type: '',
  business_phone: '',
  business_email: '',
  address: '',
  description: ''
})

const createForm = ref({
  name: '',
  business_type: '',
  owner_username: '',
  business_phone: '',
  business_email: '',
  address: '',
  description: ''
})

// View tabs for business details
const viewTabs = ref([
  { id: 'info', name: 'Business Info', icon: 'fas fa-info-circle' },
  { id: 'subscription', name: 'Subscriptions', icon: 'fas fa-credit-card' },
  { id: 'stats', name: 'Statistics', icon: 'fas fa-chart-bar' }
])

// Computed properties
const isCreateFormValid = computed(() => {
  return createForm.value.name &&
         createForm.value.business_type &&
         createForm.value.owner_username
})

// Watch for business changes to populate edit form and load subscriptions
watch(() => props.business, (newBusiness) => {
  if (newBusiness && props.mode === 'edit') {
    editForm.value = {
      name: newBusiness.name || '',
      business_type: newBusiness.business_type || '',
      business_phone: newBusiness.business_phone || '',
      business_email: newBusiness.business_email || '',
      address: newBusiness.address || '',
      description: newBusiness.description || ''
    }
  }

  // Load subscriptions when viewing business details
  if (newBusiness && props.mode === 'view') {
    loadBusinessSubscriptions(newBusiness.id)
  }
}, { immediate: true })

// Watch for mode changes to reset create form
watch(() => props.mode, (newMode) => {
  if (newMode === 'create') {
    createForm.value = {
      name: '',
      business_type: '',
      owner_username: '',
      business_phone: '',
      business_email: '',
      address: '',
      description: ''
    }
  }
  activeViewTab.value = 'info'
})

// Methods
const getModalTitle = () => {
  switch (props.mode) {
    case 'view':
      return 'Business Details'
    case 'edit':
      return 'Edit Business'
    case 'create':
      return 'Create New Business'
    case 'action':
      return 'Business Actions'
    default:
      return 'Business'
  }
}

const closeModal = () => {
  emit('close')
}

const handleSave = async () => {
  isLoading.value = true
  try {
    emit('save', editForm.value)
  } finally {
    isLoading.value = false
  }
}

const handleCreate = async () => {
  if (!isCreateFormValid.value) return

  isLoading.value = true
  try {
    emit('create', createForm.value)
  } finally {
    isLoading.value = false
  }
}

const handleStatusChange = (newStatus) => {
  emit('status-change', newStatus)
}

const loadBusinessSubscriptions = async (businessId) => {
  try {
    const subscriptions = await subscriptionService.getBusinessSubscriptions({ business_id: businessId })
    businessSubscriptions.value = subscriptions
  } catch (error) {
    console.error('Failed to load business subscriptions:', error)
    businessSubscriptions.value = []
  }
}

const getStatusClass = (status) => {
  switch (status) {
    case 'active':
      return 'bg-[#2E7D32]/10 text-[#2E7D32]'
    case 'suspended':
      return 'bg-[#FF8F00]/10 text-[#FF8F00]'
    case 'inactive':
      return 'bg-[#C62828]/10 text-[#C62828]'
    default:
      return 'bg-[#4B4B4B]/10 text-[#4B4B4B]'
  }
}

const getSubscriptionStatusClass = (status) => {
  switch (status) {
    case 'active':
      return 'bg-[#2E7D32]/10 text-[#2E7D32]'
    case 'pending':
      return 'bg-[#FF8F00]/10 text-[#FF8F00]'
    case 'expired':
      return 'bg-[#C62828]/10 text-[#C62828]'
    case 'suspended':
      return 'bg-[#FF8F00]/10 text-[#FF8F00]'
    case 'cancelled':
      return 'bg-[#4B4B4B]/10 text-[#4B4B4B]'
    default:
      return 'bg-[#4B4B4B]/10 text-[#4B4B4B]'
  }
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
