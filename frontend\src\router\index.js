import { createRouter, createWebHistory } from 'vue-router'
import LoginView from '../views/LoginView.vue'
import { useAuthStore } from '../stores/auth.js'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'login',
      component: LoginView,
      meta: { requiresGuest: true },
    },
    {
      path: '/login',
      name: 'login-explicit',
      component: LoginView,
      meta: { requiresGuest: true },
    },
    {
      path: '/forge',
      name: 'forge',
      component: () => import('../views/ForgeView.vue'),
      meta: { requiresAuth: true, role: 'forge' },
    },
    {
      path: '/pod',
      name: 'pod',
      component: () => import('../views/PodView.vue'),
      meta: { requiresAuth: true, role: 'pod' },
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue'),
      meta: { requiresGuest: true },
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/test-toggle',
      name: 'test-toggle',
      component: () => import('../views/TestToggle.vue'),
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      redirect: '/',
    },
  ],
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Check authentication status
  if (authStore.token && !authStore.user) {
    // We have a token but no user data, verify with backend
    const isValid = await authStore.checkAuth()
    if (!isValid) {
      // Token is invalid, redirect to login
      if (to.meta.requiresAuth) {
        return next('/')
      }
    }
  }

  // Handle guest-only routes (login, register)
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    // User is already authenticated, redirect to appropriate dashboard
    if (authStore.isForge) {
      return next('/forge')
    } else if (authStore.isPod) {
      return next('/pod')
    }
    return next('/pod') // Default to pod if role is unclear
  }

  // Handle protected routes
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      // Not authenticated, redirect to login
      return next('/')
    }

    // Check role-based access
    if (to.meta.role && authStore.userRole !== to.meta.role) {
      // Wrong role, redirect to appropriate dashboard
      if (authStore.isForge) {
        return next('/forge')
      } else if (authStore.isPod) {
        return next('/pod')
      }
      // If no valid role, logout and redirect to login
      await authStore.logout()
      return next('/')
    }
  }

  next()
})

export default router
